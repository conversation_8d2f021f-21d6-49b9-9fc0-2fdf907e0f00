[{"success": true, "row_count": 5000, "execution_time": 5.521, "peak_memory_mb": 0.0, "memray_file": "memray_profiles/duckdb_5953.bin", "scenario": "Wide File Projection (2/100 fields)", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1, "execution_time": 5.137, "peak_memory_mb": 0.0, "memray_file": "memray_profiles/duckdb_5329.bin", "scenario": "Wide File Full Scan", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1000, "execution_time": 2.019, "peak_memory_mb": 0.0, "memray_file": "memray_profiles/duckdb_633.bin", "scenario": "Deep File Projection (skip nested data)", "file": "memory_test_deep.json"}, {"success": true, "row_count": 1, "execution_time": 1.94, "peak_memory_mb": 0.0, "memray_file": "memray_profiles/duckdb_5020.bin", "scenario": "Deep File Full Scan", "file": "memory_test_deep.json"}]