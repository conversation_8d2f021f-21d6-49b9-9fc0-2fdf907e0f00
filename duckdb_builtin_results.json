[{"success": true, "row_count": 5000, "execution_time": 5.475, "peak_memory_mb": 1899.52, "memray_file": "memray_profiles/duckdb_7008.bin", "scenario": "Wide File Projection (2/100 fields)", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1, "execution_time": 5.102, "peak_memory_mb": 1895.424, "memray_file": "memray_profiles/duckdb_3035.bin", "scenario": "Wide File Full Scan", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1000, "execution_time": 2.324, "peak_memory_mb": 1074.176, "memray_file": "memray_profiles/duckdb_5338.bin", "scenario": "Deep File Projection (skip nested data)", "file": "memory_test_deep.json"}, {"success": true, "row_count": 1, "execution_time": 2.084, "peak_memory_mb": 1071.104, "memray_file": "memray_profiles/duckdb_8477.bin", "scenario": "Deep File Full Scan", "file": "memory_test_deep.json"}]