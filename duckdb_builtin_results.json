[{"success": true, "row_count": 5000, "start_memory": 22.5, "end_memory": 199.40625, "peak_memory": 199.40625, "memory_used": 176.90625, "execution_time": 2.694754123687744, "scenario": "Wide File Projection (2/100 fields)", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1, "start_memory": 199.421875, "end_memory": 230.5625, "peak_memory": 230.5625, "memory_used": 31.140625, "execution_time": 2.2862820625305176, "scenario": "Wide File Full Scan", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1000, "start_memory": 230.5625, "end_memory": 252.328125, "peak_memory": 252.328125, "memory_used": 21.765625, "execution_time": 0.9612736701965332, "scenario": "Deep File Projection (skip nested data)", "file": "memory_test_deep.json"}, {"success": true, "row_count": 1, "start_memory": 252.328125, "end_memory": 261.75, "peak_memory": 261.75, "memory_used": 9.421875, "execution_time": 0.9427890777587891, "scenario": "Deep File Full Scan", "file": "memory_test_deep.json"}]