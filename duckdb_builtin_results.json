[{"success": true, "row_count": 5000, "execution_time": 5.377, "peak_memory_mb": 1900.544, "memray_file": "memray_profiles/duckdb_9489.bin", "scenario": "Wide File Projection (2/100 fields)", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1, "execution_time": 5.241, "peak_memory_mb": 1895.424, "memray_file": "memray_profiles/duckdb_6307.bin", "scenario": "Wide File Full Scan", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1000, "execution_time": 2.369, "peak_memory_mb": 1073.152, "memray_file": "memray_profiles/duckdb_7648.bin", "scenario": "Deep File Projection (skip nested data)", "file": "memory_test_deep.json"}, {"success": true, "row_count": 1, "execution_time": 2.058, "peak_memory_mb": 1072.128, "memray_file": "memray_profiles/duckdb_4520.bin", "scenario": "Deep File Full Scan", "file": "memory_test_deep.json"}]