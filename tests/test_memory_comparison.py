"""
Memory usage comparison tests between DuckDB built-in JSON reader and our extension.
Uses pytest-memray for accurate memory profiling.
"""

import pytest
import duckdb


@pytest.mark.memory
class TestMemoryComparison:
    """Test memory usage comparison between DuckDB built-in and our extension."""

    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_wide_projection(self, duckdb_builtin_conn, small_wide_json):
        """Test DuckDB built-in JSON reader memory usage for wide file projection."""
        result = duckdb_builtin_conn.execute(
            f"SELECT id, name FROM read_json('{small_wide_json}')"
        ).fetchall()
        assert len(result) == 10000
        assert result[0] == (0, "User_0")

    @pytest.mark.memray(limit="500MB")
    def test_extension_wide_projection(self, duckdb_conn, small_wide_json):
        """Test our extension memory usage for wide file projection."""
        result = duckdb_conn.execute(
            f"SELECT id, name FROM json_stream('{small_wide_json}')"
        ).fetchall()
        # Note: Extension currently returns empty results, but should not crash
        # This test verifies memory usage and that the query completes
        assert isinstance(result, list)  # Should return a list, even if empty
    
    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_wide_full_scan(self, duckdb_builtin_conn, small_wide_json):
        """Test DuckDB built-in JSON reader memory usage for wide file full scan."""
        result = duckdb_builtin_conn.execute(
            f"SELECT COUNT(*) FROM read_json('{small_wide_json}')"
        ).fetchall()
        assert result[0][0] == 10000

    @pytest.mark.memray(limit="500MB")
    def test_extension_wide_full_scan(self, duckdb_conn, small_wide_json):
        """Test our extension memory usage for wide file full scan."""
        result = duckdb_conn.execute(
            f"SELECT COUNT(*) FROM json_stream('{small_wide_json}')"
        ).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)
    
    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_deep_projection(self, duckdb_builtin_conn, small_deep_json):
        """Test DuckDB built-in JSON reader memory usage for deep file projection."""
        result = duckdb_builtin_conn.execute(
            f"SELECT id, target_field FROM read_json('{small_deep_json}')"
        ).fetchall()
        assert len(result) == 2000
        assert result[0] == (0, "important_value_0")

    @pytest.mark.memray(limit="500MB")
    def test_extension_deep_projection(self, duckdb_conn, small_deep_json):
        """Test our extension memory usage for deep file projection."""
        result = duckdb_conn.execute(
            f"SELECT id, target_field FROM json_stream('{small_deep_json}')"
        ).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)

    @pytest.mark.memray(limit="5GB")
    def test_duckdb_builtin_deep_full_scan(self, duckdb_builtin_conn, small_deep_json):
        """Test DuckDB built-in JSON reader memory usage for deep file full scan."""
        result = duckdb_builtin_conn.execute(
            f"SELECT COUNT(*) FROM read_json('{small_deep_json}')"
        ).fetchall()
        assert result[0][0] == 2000

    @pytest.mark.memray(limit="500MB")
    def test_extension_deep_full_scan(self, duckdb_conn, small_deep_json):
        """Test our extension memory usage for deep file full scan."""
        result = duckdb_conn.execute(
            f"SELECT COUNT(*) FROM json_stream('{small_deep_json}')"
        ).fetchall()
        # Extension should complete without crashing
        assert isinstance(result, list)


@pytest.mark.performance
class TestPerformanceBaseline:
    """Basic performance tests to ensure reasonable execution times."""

    def test_extension_performance_wide(self, duckdb_conn, small_wide_json):
        """Test extension performance on wide JSON (should complete in reasonable time)."""
        import time
        start = time.time()

        result = duckdb_conn.execute(
            f"SELECT id, name FROM json_stream('{small_wide_json}')"
        ).fetchall()

        end = time.time()
        execution_time = end - start

        assert isinstance(result, list)
        assert execution_time < 60  # Should complete within 60 seconds for large dataset
        print(f"Extension wide projection time: {execution_time:.3f}s")

    def test_extension_performance_deep(self, duckdb_conn, small_deep_json):
        """Test extension performance on deep JSON (should complete in reasonable time)."""
        import time
        start = time.time()

        result = duckdb_conn.execute(
            f"SELECT id, target_field FROM json_stream('{small_deep_json}')"
        ).fetchall()

        end = time.time()
        execution_time = end - start

        assert isinstance(result, list)
        assert execution_time < 60  # Should complete within 60 seconds for large dataset
        print(f"Extension deep projection time: {execution_time:.3f}s")


@pytest.mark.projection
class TestProjectionPushdown:
    """Test projection pushdown functionality."""

    def test_projection_no_crash_wide(self, duckdb_conn, small_wide_json):
        """Verify projection queries don't crash on wide data."""
        # Test that projection queries complete without errors
        result = duckdb_conn.execute(
            f"SELECT id FROM json_stream('{small_wide_json}') LIMIT 5"
        ).fetchall()
        assert isinstance(result, list)

    def test_projection_no_crash_deep(self, duckdb_conn, small_deep_json):
        """Verify projection queries don't crash on deep data."""
        # Test that projection queries complete without errors
        result = duckdb_conn.execute(
            f"SELECT id FROM json_stream('{small_deep_json}') LIMIT 5"
        ).fetchall()
        assert isinstance(result, list)
