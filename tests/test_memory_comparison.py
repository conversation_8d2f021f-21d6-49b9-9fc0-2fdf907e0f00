"""
Memory usage comparison tests between DuckDB built-in JSON reader and our extension.
Uses pytest-memray for accurate memory profiling.
"""

import pytest
import duckdb


@pytest.mark.memory
class TestMemoryComparison:
    """Test memory usage comparison between DuckDB built-in and our extension."""
    
    @pytest.mark.memray(limit="2GB")
    def test_duckdb_builtin_wide_projection(self, duckdb_builtin_conn, small_wide_json):
        """Test DuckDB built-in JSON reader memory usage for wide file projection."""
        result = duckdb_builtin_conn.execute(
            f"SELECT id, name FROM read_json('{small_wide_json}')"
        ).fetchall()
        assert len(result) == 100
        assert result[0] == (0, "User_0")
    
    @pytest.mark.memray(limit="2GB")
    def test_extension_wide_projection(self, duckdb_conn, small_wide_json):
        """Test our extension memory usage for wide file projection."""
        result = duckdb_conn.execute(
            f"SELECT id, name FROM json_stream('{small_wide_json}')"
        ).fetchall()
        assert len(result) == 100
        assert result[0] == (0, "User_0")
    
    @pytest.mark.memray(limit="2GB")
    def test_duckdb_builtin_wide_full_scan(self, duckdb_builtin_conn, small_wide_json):
        """Test DuckDB built-in JSON reader memory usage for wide file full scan."""
        result = duckdb_builtin_conn.execute(
            f"SELECT COUNT(*) FROM read_json('{small_wide_json}')"
        ).fetchall()
        assert result[0][0] == 100
    
    @pytest.mark.memray(limit="2GB")
    def test_extension_wide_full_scan(self, duckdb_conn, small_wide_json):
        """Test our extension memory usage for wide file full scan."""
        result = duckdb_conn.execute(
            f"SELECT COUNT(*) FROM json_stream('{small_wide_json}')"
        ).fetchall()
        assert result[0][0] == 100
    
    @pytest.mark.memray(limit="2GB")
    def test_duckdb_builtin_deep_projection(self, duckdb_builtin_conn, small_deep_json):
        """Test DuckDB built-in JSON reader memory usage for deep file projection."""
        result = duckdb_builtin_conn.execute(
            f"SELECT id, target_field FROM read_json('{small_deep_json}')"
        ).fetchall()
        assert len(result) == 50
        assert result[0] == (0, "important_value_0")
    
    @pytest.mark.memray(limit="2GB")
    def test_extension_deep_projection(self, duckdb_conn, small_deep_json):
        """Test our extension memory usage for deep file projection."""
        result = duckdb_conn.execute(
            f"SELECT id, target_field FROM json_stream('{small_deep_json}')"
        ).fetchall()
        assert len(result) == 50
        assert result[0] == (0, "important_value_0")
    
    @pytest.mark.memray(limit="2GB")
    def test_duckdb_builtin_deep_full_scan(self, duckdb_builtin_conn, small_deep_json):
        """Test DuckDB built-in JSON reader memory usage for deep file full scan."""
        result = duckdb_builtin_conn.execute(
            f"SELECT COUNT(*) FROM read_json('{small_deep_json}')"
        ).fetchall()
        assert result[0][0] == 50
    
    @pytest.mark.memray(limit="2GB")
    def test_extension_deep_full_scan(self, duckdb_conn, small_deep_json):
        """Test our extension memory usage for deep file full scan."""
        result = duckdb_conn.execute(
            f"SELECT COUNT(*) FROM json_stream('{small_deep_json}')"
        ).fetchall()
        assert result[0][0] == 50


@pytest.mark.projection
class TestProjectionPushdown:
    """Test projection pushdown functionality."""
    
    def test_projection_correctness_wide(self, duckdb_conn, duckdb_builtin_conn, small_wide_json):
        """Verify projection results match between extension and built-in reader."""
        # Test projection
        extension_result = duckdb_conn.execute(
            f"SELECT id, important_field FROM json_stream('{small_wide_json}') ORDER BY id LIMIT 10"
        ).fetchall()
        
        builtin_result = duckdb_builtin_conn.execute(
            f"SELECT id, important_field FROM read_json('{small_wide_json}') ORDER BY id LIMIT 10"
        ).fetchall()
        
        assert extension_result == builtin_result
        assert len(extension_result) == 10
        assert extension_result[0] == (0, "important_value_0")
    
    def test_projection_correctness_deep(self, duckdb_conn, duckdb_builtin_conn, small_deep_json):
        """Verify deep projection results match between extension and built-in reader."""
        extension_result = duckdb_conn.execute(
            f"SELECT id, target_field FROM json_stream('{small_deep_json}') ORDER BY id LIMIT 10"
        ).fetchall()
        
        builtin_result = duckdb_builtin_conn.execute(
            f"SELECT id, target_field FROM read_json('{small_deep_json}') ORDER BY id LIMIT 10"
        ).fetchall()
        
        assert extension_result == builtin_result
        assert len(extension_result) == 10
        assert extension_result[0] == (0, "important_value_0")
    
    def test_complex_projection(self, duckdb_conn, small_deep_json):
        """Test complex nested projections work correctly."""
        result = duckdb_conn.execute(f"""
            SELECT id, company.name, dept_name 
            FROM (
                SELECT id, company, unnest(company.departments, recursive:=true) AS dept_name
                FROM json_stream('{small_deep_json}')
            )
            ORDER BY id, dept_name
            LIMIT 5
        """).fetchall()
        
        assert len(result) == 5
        # Verify structure
        assert result[0][0] == 0  # id
        assert "Company_0" in str(result[0][1])  # company.name
        assert "Dept_" in str(result[0][2])  # dept_name


@pytest.mark.performance
class TestPerformanceBaseline:
    """Basic performance tests to ensure reasonable execution times."""
    
    def test_extension_performance_wide(self, duckdb_conn, small_wide_json):
        """Test extension performance on wide JSON (should complete in reasonable time)."""
        import time
        start = time.time()
        
        result = duckdb_conn.execute(
            f"SELECT id, name FROM json_stream('{small_wide_json}')"
        ).fetchall()
        
        end = time.time()
        execution_time = end - start
        
        assert len(result) == 100
        assert execution_time < 10  # Should complete within 10 seconds
        print(f"Extension wide projection time: {execution_time:.3f}s")
    
    def test_extension_performance_deep(self, duckdb_conn, small_deep_json):
        """Test extension performance on deep JSON (should complete in reasonable time)."""
        import time
        start = time.time()
        
        result = duckdb_conn.execute(
            f"SELECT id, target_field FROM json_stream('{small_deep_json}')"
        ).fetchall()
        
        end = time.time()
        execution_time = end - start
        
        assert len(result) == 50
        assert execution_time < 10  # Should complete within 10 seconds
        print(f"Extension deep projection time: {execution_time:.3f}s")
