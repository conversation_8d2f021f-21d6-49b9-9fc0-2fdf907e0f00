"""
Pytest configuration and fixtures for JSON streaming extension tests.
"""

import pytest
import duckdb
import json
import os
from pathlib import Path


@pytest.fixture(scope="session")
def extension_path():
    """Path to the JSON streaming extension."""
    path = Path(__file__).parent.parent.joinpath("build/release/json_stream.duckdb_extension")
    if not path.exists():
        pytest.skip("Extension not found. Run 'make release' first.")
    return str(path)


@pytest.fixture
def duckdb_conn(extension_path):
    """DuckDB connection with JSON streaming extension loaded."""
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
    conn.execute(f"LOAD '{extension_path}'")
    yield conn
    conn.close()


@pytest.fixture
def duckdb_builtin_conn():
    """DuckDB connection for built-in JSON reader comparison."""
    conn = duckdb.connect()
    yield conn
    conn.close()


@pytest.fixture(scope="session")
def small_wide_json():
    """Create a large wide JSON file for memory stress testing."""
    data = []
    for i in range(10000):  # 10K rows for GB-scale memory usage
        row = {
            "id": i,
            "name": f"User_{i}",
            "important_field": f"important_value_{i}",
        }
        # Add 50 unused fields with substantial data
        for j in range(50):
            row[f"unused_field_{j}"] = {
                "data": f"unused_data_{i}_{j}" * 10,  # Larger strings
                "nested": {
                    "deep_field": f"deep_value_{i}_{j}" * 5,
                    "array": [f"item_{k}_{i}_{j}" for k in range(10)],  # Larger arrays
                    "metadata": {
                        "timestamp": f"2024-01-{(i % 28) + 1:02d}T{(j % 24):02d}:00:00Z",
                        "tags": [f"tag_{k}" for k in range(5)],
                        "properties": {f"prop_{k}": f"value_{k}_{i}_{j}" for k in range(5)}
                    }
                }
            }
        data.append(row)

    # Create a persistent file for the session
    filename = "test_large_wide.json"
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)

    yield filename

    # Cleanup after session
    try:
        os.unlink(filename)
    except:
        pass


@pytest.fixture(scope="session")
def small_deep_json():
    """Create a large deeply nested JSON file for memory stress testing."""
    data = []
    for i in range(2000):  # 2K rows with deep nesting for GB-scale memory usage
        row = {
            "id": i,
            "target_field": f"important_value_{i}",
            "company": {
                "id": i,
                "name": f"Company_{i}",
                "metadata": f"company_metadata_{i}" * 20,  # Larger metadata
                "departments": [
                    {
                        "name": f"Dept_{j}",
                        "budget": f"budget_data_{i}_{j}" * 15,
                        "teams": [
                            {
                                "name": f"Team_{k}",
                                "description": f"team_description_{i}_{j}_{k}" * 10,
                                "projects": [
                                    {
                                        "title": f"Project_{l}",
                                        "description": f"project_description_{i}_{j}_{k}_{l}" * 8,
                                        "metrics": {
                                            "performance": 0.95,
                                            "large_data": f"data_{i}_{j}_{k}_{l}" * 12,
                                            "analytics": {
                                                "views": [f"view_{m}_{i}_{j}_{k}_{l}" for m in range(5)],
                                                "reports": {f"report_{m}": f"report_data_{m}_{i}_{j}_{k}_{l}" * 6 for m in range(3)},
                                                "logs": [f"log_entry_{m}_{i}_{j}_{k}_{l}" * 4 for m in range(8)]
                                            }
                                        }
                                    }
                                    for l in range(3)  # 3 projects per team
                                ]
                            }
                            for k in range(4)  # 4 teams per department
                        ]
                    }
                    for j in range(3)  # 3 departments per company
                ]
            }
        }
        data.append(row)

    # Create a persistent file for the session
    filename = "test_large_deep.json"
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)

    yield filename

    # Cleanup after session
    try:
        os.unlink(filename)
    except:
        pass
