"""
Pytest configuration and fixtures for JSON streaming extension tests.
"""

import pytest
import duckdb
import json
import os
from pathlib import Path


@pytest.fixture(scope="session")
def extension_path():
    """Path to the JSON streaming extension."""
    path = Path(__file__).parent.parent.joinpath("build/release/json_stream.duckdb_extension")
    if not path.exists():
        pytest.skip("Extension not found. Run 'make release' first.")
    return str(path)


@pytest.fixture
def duckdb_conn(extension_path):
    """DuckDB connection with JSON streaming extension loaded."""
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
    conn.execute(f"LOAD '{extension_path}'")
    yield conn
    conn.close()


@pytest.fixture
def duckdb_builtin_conn():
    """DuckDB connection for built-in JSON reader comparison."""
    conn = duckdb.connect()
    yield conn
    conn.close()


@pytest.fixture(scope="session")
def small_wide_json():
    """Create a small wide JSON file for testing."""
    data = []
    for i in range(50):  # Small dataset for fast tests
        row = {
            "id": i,
            "name": f"User_{i}",
            "important_field": f"important_value_{i}",
        }
        # Add 10 unused fields
        for j in range(10):
            row[f"unused_field_{j}"] = {
                "data": f"unused_data_{i}_{j}",
                "nested": {
                    "deep_field": f"deep_value_{i}_{j}",
                    "array": [f"item_{k}" for k in range(2)]
                }
            }
        data.append(row)
    
    # Create a persistent file for the session
    filename = "test_small_wide.json"
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)
    
    yield filename
    
    # Cleanup after session
    try:
        os.unlink(filename)
    except:
        pass


@pytest.fixture(scope="session")
def small_deep_json():
    """Create a small deeply nested JSON file for testing."""
    data = []
    for i in range(25):  # Small dataset for fast tests
        row = {
            "id": i,
            "target_field": f"important_value_{i}",
            "company": {
                "id": i,
                "name": f"Company_{i}",
                "departments": [
                    {
                        "name": f"Dept_{j}",
                        "teams": [
                            {
                                "name": f"Team_{k}",
                                "projects": [
                                    {
                                        "title": f"Project_{l}",
                                        "metrics": {
                                            "performance": 0.95,
                                            "large_data": f"data_{i}_{j}_{k}_{l}"
                                        }
                                    }
                                    for l in range(1)
                                ]
                            }
                            for k in range(1)
                        ]
                    }
                    for j in range(1)
                ]
            }
        }
        data.append(row)
    
    # Create a persistent file for the session
    filename = "test_small_deep.json"
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)
    
    yield filename
    
    # Cleanup after session
    try:
        os.unlink(filename)
    except:
        pass
