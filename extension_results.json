[{"success": true, "row_count": 5000, "start_memory": 22.4375, "end_memory": 37.484375, "peak_memory": 37.484375, "memory_used": 15.046875, "execution_time": 82.26951289176941, "scenario": "Wide File Projection (2/100 fields)", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1, "start_memory": 37.546875, "end_memory": 38.9375, "peak_memory": 38.9375, "memory_used": 1.390625, "execution_time": 80.75708484649658, "scenario": "Wide File Full Scan", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1000, "start_memory": 38.9375, "end_memory": 37.21875, "peak_memory": 37.21875, "memory_used": -1.71875, "execution_time": 16.47682499885559, "scenario": "Deep File Projection (skip nested data)", "file": "memory_test_deep.json"}, {"success": true, "row_count": 1, "start_memory": 37.21875, "end_memory": 36.40625, "peak_memory": 36.40625, "memory_used": -0.8125, "execution_time": 16.23366141319275, "scenario": "Deep File Full Scan", "file": "memory_test_deep.json"}]