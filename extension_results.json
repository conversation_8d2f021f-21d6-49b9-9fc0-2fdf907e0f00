[{"success": true, "row_count": 5000, "execution_time": 93.44, "peak_memory_mb": 6150.144, "memray_file": "memray_profiles/extension_418.bin", "scenario": "Wide File Projection (2/100 fields)", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1, "execution_time": 93.017, "peak_memory_mb": 6148.096, "memray_file": "memray_profiles/extension_1813.bin", "scenario": "Wide File Full Scan", "file": "memory_test_wide.json"}, {"success": true, "row_count": 1000, "execution_time": 18.519, "peak_memory_mb": 1179.648, "memray_file": "memray_profiles/extension_1486.bin", "scenario": "Deep File Projection (skip nested data)", "file": "memory_test_deep.json"}, {"success": true, "row_count": 1, "execution_time": 18.317, "peak_memory_mb": 1177.6, "memray_file": "memray_profiles/extension_825.bin", "scenario": "Deep File Full Scan", "file": "memory_test_deep.json"}]