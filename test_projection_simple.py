#!/usr/bin/env python3
"""
Simple test script for projection pushdown functionality.
"""

import duckdb
import json
import tempfile
import time

def create_test_data():
    """Create test JSON data."""
    return [
        {
            "user": {
                "id": 1,
                "name": "<PERSON>",
                "profile": {
                    "email": "<EMAIL>",
                    "preferences": {"theme": "dark"}
                }
            },
            "posts": [
                {"title": "Hello World", "likes": 10},
                {"title": "JSON Streaming", "likes": 25}
            ]
        },
        {
            "user": {
                "id": 2,
                "name": "<PERSON>", 
                "profile": {
                    "email": "<EMAIL>",
                    "preferences": {"theme": "light"}
                }
            },
            "posts": [
                {"title": "DuckDB Extensions", "likes": 50}
            ]
        }
    ]

def create_wide_data():
    """Create wide JSON data for performance testing."""
    data = []
    for i in range(50):
        row = {"id": i, "important": f"value_{i}"}
        # Add many unused fields
        for j in range(20):
            row[f"unused_{j}"] = f"data_{i}_{j}"
        data.append(row)
    return data

def test_projection_pushdown():
    """Test projection pushdown functionality."""
    print("🧪 Testing Projection Pushdown Functionality")
    print("=" * 50)
    
    # Create DuckDB connection with unsigned extensions enabled
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
    conn.execute("LOAD './build/debug/json_stream.duckdb_extension'")
    
    # Create test files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(create_test_data(), f)
        test_file = f.name
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(create_wide_data(), f)
        wide_file = f.name
    
    try:
        # Test 1: Single column projection
        print("\n✅ Test 1: Single Column Projection")
        result = conn.execute(f"SELECT user.name FROM json_stream('{test_file}')").fetchall()
        print(f"   Result: {result}")
        assert result == [('Alice',), ('Bob',)]
        print("   ✓ PASSED")
        
        # Test 2: Multiple column projection  
        print("\n✅ Test 2: Multiple Column Projection")
        result = conn.execute(f"SELECT user.id, user.name FROM json_stream('{test_file}')").fetchall()
        print(f"   Result: {result}")
        assert result == [(1, 'Alice'), (2, 'Bob')]
        print("   ✓ PASSED")
        
        # Test 3: Nested field projection
        print("\n✅ Test 3: Nested Field Projection")
        result = conn.execute(f"SELECT user.profile.email FROM json_stream('{test_file}')").fetchall()
        print(f"   Result: {result}")
        assert result == [('<EMAIL>',), ('<EMAIL>',)]
        print("   ✓ PASSED")
        
        # Test 4: Complex structure projection
        print("\n✅ Test 4: Complex Structure Projection")
        result = conn.execute(f"SELECT posts FROM json_stream('{test_file}')").fetchall()
        print(f"   Result length: {len(result)}")
        assert len(result) == 2
        print("   ✓ PASSED")
        
        # Test 5: Performance comparison
        print("\n✅ Test 5: Performance Comparison")
        
        # Full scan
        start = time.time()
        conn.execute(f"SELECT * FROM json_stream('{wide_file}')").fetchall()
        full_time = time.time() - start
        
        # Projected scan
        start = time.time()
        result = conn.execute(f"SELECT id, important FROM json_stream('{wide_file}')").fetchall()
        proj_time = time.time() - start
        
        print(f"   Full scan time: {full_time:.4f}s")
        print(f"   Projected scan time: {proj_time:.4f}s")
        print(f"   Projected result count: {len(result)}")
        assert len(result) == 50
        assert result[0] == (0, 'value_0')
        print("   ✓ PASSED")
        
        # Test 6: Projection with unnest
        print("\n✅ Test 6: Projection with Unnest")
        result = conn.execute(f"""
            SELECT title, likes 
            FROM (SELECT unnest(posts, recursive:=true) FROM json_stream('{test_file}'))
        """).fetchall()
        print(f"   Result: {result}")
        expected = [('Hello World', 10), ('JSON Streaming', 25), ('DuckDB Extensions', 50)]
        assert result == expected
        print("   ✓ PASSED")
        
        print("\n🎉 All Projection Pushdown Tests PASSED!")
        print("=" * 50)
        
    finally:
        # Cleanup
        import os
        try:
            os.unlink(test_file)
            os.unlink(wide_file)
        except:
            pass

if __name__ == "__main__":
    test_projection_pushdown()
