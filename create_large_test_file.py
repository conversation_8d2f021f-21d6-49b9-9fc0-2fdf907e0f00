#!/usr/bin/env python3
"""
Create a large JSON file for performance testing projection pushdown.
"""

import json

def create_large_json_file():
    """Create a large JSON file with many fields for performance testing."""
    data = []
    
    for i in range(1000):  # 1000 rows
        row = {
            "id": i,
            "name": f"User_{i}",
            "important_field": f"important_value_{i}",
            "user_profile": {
                "email": f"user{i}@example.com",
                "age": 20 + (i % 50),
                "preferences": {
                    "theme": "dark" if i % 2 == 0 else "light",
                    "notifications": i % 3 == 0,
                    "language": "en"
                }
            },
            "posts": [
                {
                    "title": f"Post {j} by User {i}",
                    "content": f"This is post content {j} by user {i}. " * 10,  # Make it larger
                    "likes": (i * j) % 100,
                    "comments": [
                        {"author": f"commenter_{k}", "text": f"Comment {k} on post {j}"}
                        for k in range(min(5, i % 6))  # Variable number of comments
                    ]
                }
                for j in range(min(3, (i % 4) + 1))  # Variable number of posts
            ],
            "metadata": {
                "created_at": f"2024-01-{(i % 28) + 1:02d}",
                "updated_at": f"2024-02-{(i % 28) + 1:02d}",
                "tags": [f"tag_{k}" for k in range(i % 5)],
                "category": f"category_{i % 10}"
            }
        }
        
        # Add many unused fields to test projection efficiency
        for j in range(20):
            row[f"unused_field_{j}"] = {
                "data": f"unused_data_{i}_{j}",
                "nested": {
                    "deep_field": f"deep_value_{i}_{j}",
                    "array": [f"item_{k}" for k in range(5)]
                }
            }
        
        data.append(row)
    
    # Write to file
    with open('large_test.json', 'w') as f:
        json.dump(data, f)
    
    print(f"Created large_test.json with {len(data)} rows")
    print("Each row has:")
    print("- 4 main fields (id, name, important_field, user_profile)")
    print("- 1 posts array with nested comments")
    print("- 1 metadata object")
    print("- 20 unused nested objects")
    print("Total: ~26 top-level fields per row")

if __name__ == "__main__":
    create_large_json_file()
