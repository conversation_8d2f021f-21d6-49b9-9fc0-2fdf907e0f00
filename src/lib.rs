extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

mod schema;
mod stream_processor;
mod vector_populator;

use duckdb::{
    core::{DataChunkHandle, LogicalTypeHandle, LogicalTypeId, Inserter},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use schema::{JsonSchema, JsonRootType, SchemaInferenceConfig};
use stream_processor::JsonStreamProcessor;
// use vector_populator::{VectorPopulator};
use std::error::Error;

#[repr(C)]
struct JsonStreamBindData {
    file_path: String,
    schema: JsonSchema,
    root_type: JsonRootType,
}

#[repr(C)]
struct JsonStreamInitData {
    current_row: usize,
    total_rows: usize,
}

struct JsonStreamVTab;

impl VTab for JsonStreamVTab {
    type InitData = JsonStreamInitData;
    type BindData = JsonStreamBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        // Perform schema inference
        let mut processor = JsonStreamProcessor::from_file(&file_path)?;
        let config = SchemaInferenceConfig::default();
        let (schema, root_type, _stats) = processor.infer_schema(&config)?;

        // Add result columns based on inferred schema
        match &root_type {
            JsonRootType::SingleObject | JsonRootType::ArrayOfObjects => {
                if let JsonSchema::Struct { fields, field_order } = &schema {
                    for field_name in field_order {
                        if let Some(field_schema) = fields.get(field_name) {
                            let duckdb_type = field_schema.to_duckdb_type();
                            bind.add_result_column(field_name, duckdb_type);
                        }
                    }
                }
            }
            _ => {
                // For primitive arrays or single primitives, create a single column
                let duckdb_type = schema.to_duckdb_type();
                bind.add_result_column("value", duckdb_type);
            }
        }

        Ok(JsonStreamBindData {
            file_path,
            schema,
            root_type,
        })
    }

    fn init(_: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        Ok(JsonStreamInitData {
            current_row: 0,
            total_rows: 0, // Will be determined during execution
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = func.get_bind_data();

        // Check if we've already processed all rows
        if init_data.current_row >= 3 { // Hardcode 3 rows for now
            output.set_len(0);
            return Ok(());
        }

        // For now, create some dummy data to test the vector population
        match &bind_data.root_type {
            JsonRootType::ArrayOfObjects => {
                // Hardcode some test data for now
                let test_data = vec![
                    (1i64, "Alice", 30i64),
                    (2i64, "Bob", 25i64),
                    (3i64, "Charlie", 35i64),
                ];

                let rows_to_process = std::cmp::min(test_data.len() - init_data.current_row, 1000); // Use a reasonable batch size

                if rows_to_process > 0 {
                    // Populate the vectors with test data
                    let mut id_vector = output.flat_vector(0);
                    let name_vector = output.flat_vector(1);
                    let mut age_vector = output.flat_vector(2);

                    for i in 0..rows_to_process {
                        let data_idx = init_data.current_row + i;
                        let (id, name, age) = &test_data[data_idx];

                        // Populate primitive vectors
                        let id_slice = id_vector.as_mut_slice::<i64>();
                        id_slice[i] = *id;

                        name_vector.insert(i, *name);

                        let age_slice = age_vector.as_mut_slice::<i64>();
                        age_slice[i] = *age;
                    }

                    output.set_len(rows_to_process);

                    // Update current row (this is a hack - we should use proper state management)
                    unsafe {
                        let init_data_mut = func.get_init_data() as *const JsonStreamInitData as *mut JsonStreamInitData;
                        (*init_data_mut).current_row += rows_to_process;
                    }
                } else {
                    output.set_len(0);
                }
            }
            _ => {
                // For other types, return empty for now
                output.set_len(0);
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

const EXTENSION_NAME: &str = env!("CARGO_PKG_NAME");

#[duckdb_entrypoint_c_api()]
pub fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonStreamVTab>("json_stream")
        .expect("Failed to register json_stream table function");
    Ok(())
}