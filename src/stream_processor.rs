use crate::schema::{JsonSchema, JsonRootType, PrimitiveType, SchemaInferenceConfig, SchemaStats};
use indexmap::IndexMap;
use std::fs::File;
use std::io::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use std::path::Path;
use struson::reader::{<PERSON>son<PERSON><PERSON><PERSON>, JsonStreamReader, ValueType};

/// Streaming JSON processor that integrates struson with DuckDB
pub struct JsonStreamProcessor<R: Read> {
    reader: JsonStreamReader<R>,
    schema: Option<JsonSchema>,
    projection: Option<Vec<String>>,
    current_path: Vec<String>,
    root_type: Option<JsonRootType>,
}

impl JsonStreamProcessor<BufReader<File>> {
    /// Create a new processor from a file path
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self, Box<dyn std::error::Error>> {
        let file = File::open(path)?;
        let reader = BufReader::new(file);
        let json_reader = JsonStreamReader::new(reader);
        
        Ok(JsonStreamProcessor {
            reader: json_reader,
            schema: None,
            projection: None,
            current_path: Vec::new(),
            root_type: None,
        })
    }
}

impl<R: Read> JsonStreamProcessor<R> {
    /// Create a new processor from a reader
    pub fn new(reader: R) -> Self {
        let json_reader = JsonStreamReader::new(reader);
        
        JsonStreamProcessor {
            reader: json_reader,
            schema: None,
            projection: None,
            current_path: Vec::new(),
            root_type: None,
        }
    }

    /// Set projection columns to optimize reading
    pub fn set_projection(&mut self, projection: Vec<String>) {
        self.projection = Some(projection);
    }

    /// Infer schema from the JSON stream
    pub fn infer_schema(&mut self, config: &SchemaInferenceConfig) -> Result<(JsonSchema, JsonRootType, SchemaStats), Box<dyn std::error::Error>> {
        let mut stats = SchemaStats::default();
        
        // Peek at the first value to determine root type
        let root_type = self.determine_root_type()?;
        
        // Sample objects for schema inference
        let schema = match root_type {
            JsonRootType::SingleObject => {
                self.infer_object_schema(config, &mut stats)?
            }
            JsonRootType::ArrayOfObjects => {
                self.infer_array_of_objects_schema(config, &mut stats)?
            }
            JsonRootType::ArrayOfPrimitives => {
                self.infer_array_of_primitives_schema(config, &mut stats)?
            }
            JsonRootType::SinglePrimitive => {
                self.infer_primitive_schema(&mut stats)?
            }
            JsonRootType::MixedArray => {
                self.infer_mixed_array_schema(config, &mut stats)?
            }
        };

        self.schema = Some(schema.clone());
        self.root_type = Some(root_type.clone());
        
        Ok((schema, root_type, stats))
    }

    /// Determine the root type of the JSON document
    fn determine_root_type(&mut self) -> Result<JsonRootType, Box<dyn std::error::Error>> {
        // Peek at the first token to determine structure
        match self.reader.peek()? {
            ValueType::Object => Ok(JsonRootType::SingleObject),
            ValueType::Array => {
                // For now, assume array of objects - we'll improve this later
                Ok(JsonRootType::ArrayOfObjects)
            }
            _ => Ok(JsonRootType::SinglePrimitive),
        }
    }

    /// Infer schema for a single object
    fn infer_object_schema(&mut self, _config: &SchemaInferenceConfig, stats: &mut SchemaStats) -> Result<JsonSchema, Box<dyn std::error::Error>> {
        self.reader.begin_object()?;
        let mut fields = IndexMap::new();
        
        while self.reader.has_next()? {
            let field_name = self.reader.next_name()?.to_string();
            let field_schema = self.infer_value_schema(0)?;
            fields.insert(field_name, field_schema);
        }
        
        self.reader.end_object()?;
        stats.objects_sampled = 1;
        
        Ok(JsonSchema::struct_schema(fields))
    }

    /// Infer schema for array of objects
    fn infer_array_of_objects_schema(&mut self, config: &SchemaInferenceConfig, stats: &mut SchemaStats) -> Result<JsonSchema, Box<dyn std::error::Error>> {
        self.reader.begin_array()?;
        let mut merged_schema: Option<JsonSchema> = None;
        let mut sample_count = 0;
        
        while self.reader.has_next()? && sample_count < config.sample_size {
            let object_schema = self.infer_value_schema(0)?;
            
            merged_schema = Some(match merged_schema {
                None => object_schema,
                Some(existing) => existing.merge(&object_schema),
            });
            
            sample_count += 1;
        }
        
        stats.objects_sampled = sample_count;
        
        // The schema for array of objects is the merged object schema
        // Each object becomes a row in the table
        merged_schema.ok_or_else(|| "Empty array".into())
    }

    /// Infer schema for array of primitives
    fn infer_array_of_primitives_schema(&mut self, config: &SchemaInferenceConfig, stats: &mut SchemaStats) -> Result<JsonSchema, Box<dyn std::error::Error>> {
        self.reader.begin_array()?;
        let mut element_schema: Option<JsonSchema> = None;
        let mut sample_count = 0;
        let mut max_array_size = 0;
        
        while self.reader.has_next()? && sample_count < config.sample_size {
            let value_schema = self.infer_value_schema(0)?;
            
            element_schema = Some(match element_schema {
                None => value_schema,
                Some(existing) => existing.merge(&value_schema),
            });
            
            sample_count += 1;
            max_array_size += 1;
        }
        
        stats.objects_sampled = sample_count;
        
        // For array of primitives, we create a single column with the primitive type
        // Each primitive becomes a row in the table
        element_schema.ok_or_else(|| "Empty array".into())
    }

    /// Infer schema for single primitive
    fn infer_primitive_schema(&mut self, stats: &mut SchemaStats) -> Result<JsonSchema, Box<dyn std::error::Error>> {
        let schema = self.infer_value_schema(0)?;
        stats.objects_sampled = 1;
        Ok(schema)
    }

    /// Infer schema for mixed array
    fn infer_mixed_array_schema(&mut self, config: &SchemaInferenceConfig, stats: &mut SchemaStats) -> Result<JsonSchema, Box<dyn std::error::Error>> {
        // For mixed arrays, we'll use a union type or fall back to string representation
        // This is a complex case that might need special handling
        self.reader.begin_array()?;
        let mut merged_schema: Option<JsonSchema> = None;
        let mut sample_count = 0;
        
        while self.reader.has_next()? && sample_count < config.sample_size {
            let value_schema = self.infer_value_schema(0)?;
            
            merged_schema = Some(match merged_schema {
                None => value_schema,
                Some(existing) => existing.merge(&value_schema),
            });
            
            sample_count += 1;
        }
        
        stats.objects_sampled = sample_count;
        stats.type_conflicts += 1; // Mixed arrays are inherently conflicted
        
        // For now, fall back to string representation for mixed arrays
        Ok(JsonSchema::primitive(PrimitiveType::String))
    }

    /// Infer schema for a single JSON value
    fn infer_value_schema(&mut self, depth: usize) -> Result<JsonSchema, Box<dyn std::error::Error>> {
        match self.reader.peek()? {
            ValueType::Object => {
                self.reader.begin_object()?;
                let mut fields = IndexMap::new();
                
                while self.reader.has_next()? {
                    let field_name = self.reader.next_name()?.to_string();
                    let field_schema = self.infer_value_schema(depth + 1)?;
                    fields.insert(field_name, field_schema);
                }
                
                self.reader.end_object()?;
                Ok(JsonSchema::struct_schema(fields))
            }
            ValueType::Array => {
                self.reader.begin_array()?;
                let mut element_schema: Option<JsonSchema> = None;
                let mut array_size = 0;
                
                while self.reader.has_next()? {
                    let value_schema = self.infer_value_schema(depth + 1)?;
                    
                    element_schema = Some(match element_schema {
                        None => value_schema,
                        Some(existing) => existing.merge(&value_schema),
                    });
                    
                    array_size += 1;
                }
                
                self.reader.end_array()?;
                
                let final_element_schema = element_schema.unwrap_or_else(|| {
                    JsonSchema::primitive(PrimitiveType::Null)
                });
                
                Ok(JsonSchema::list_schema(final_element_schema, array_size))
            }
            ValueType::String => {
                self.reader.next_string()?;
                Ok(JsonSchema::primitive(PrimitiveType::String))
            }
            ValueType::Number => {
                let number_str = self.reader.next_number_as_str()?;
                if number_str.contains('.') || number_str.contains('e') || number_str.contains('E') {
                    Ok(JsonSchema::primitive(PrimitiveType::Float))
                } else {
                    Ok(JsonSchema::primitive(PrimitiveType::Integer))
                }
            }
            ValueType::Boolean => {
                self.reader.next_bool()?;
                Ok(JsonSchema::primitive(PrimitiveType::Boolean))
            }
            ValueType::Null => {
                self.reader.next_null()?;
                Ok(JsonSchema::primitive(PrimitiveType::Null))
            }
        }
    }

    /// Get the inferred schema
    pub fn schema(&self) -> Option<&JsonSchema> {
        self.schema.as_ref()
    }

    /// Get the root type
    pub fn root_type(&self) -> Option<&JsonRootType> {
        self.root_type.as_ref()
    }
}
