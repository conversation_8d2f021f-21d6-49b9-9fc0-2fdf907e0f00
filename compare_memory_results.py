#!/usr/bin/env python3
"""
Compare memory results from independent DuckDB and extension tests.
"""

import json
import os

def load_results(filename):
    """Load test results from JSON file."""
    if not os.path.exists(filename):
        print(f"❌ Results file not found: {filename}")
        return None
    
    with open(filename, 'r') as f:
        return json.load(f)

def compare_results():
    """Compare memory usage between DuckDB built-in and our extension."""
    print("🔍 Memory Usage Comparison - Independent Test Results")
    print("=" * 70)
    
    # Load results
    duckdb_results = load_results("duckdb_builtin_results.json")
    extension_results = load_results("extension_results.json")
    
    if not duckdb_results or not extension_results:
        print("❌ Missing results files. Run the individual test scripts first.")
        return
    
    # Filter successful tests
    duckdb_success = [r for r in duckdb_results if r.get('success', False)]
    extension_success = [r for r in extension_results if r.get('success', False)]
    
    if not duckdb_success or not extension_success:
        print("❌ No successful tests found in results.")
        return
    
    # Compare by scenario
    print("\n📊 Detailed Comparison by <PERSON><PERSON><PERSON>:")
    print("=" * 70)
    
    comparisons = []
    
    for duck_result in duckdb_success:
        scenario = duck_result['scenario']
        
        # Find matching extension result
        ext_result = next((r for r in extension_success if r['scenario'] == scenario), None)
        
        if ext_result:
            duck_memory = duck_result['memory_used']
            ext_memory = ext_result['memory_used']
            duck_time = duck_result['execution_time']
            ext_time = ext_result['execution_time']
            
            memory_diff = duck_memory - ext_memory
            memory_savings_pct = (memory_diff / abs(duck_memory) * 100) if duck_memory != 0 else 0
            
            time_diff = duck_time - ext_time
            time_savings_pct = (time_diff / duck_time * 100) if duck_time > 0 else 0
            
            print(f"\n📈 {scenario}:")
            print(f"   DuckDB Built-in:")
            print(f"     Memory: {duck_memory:+.1f} MB")
            print(f"     Time:   {duck_time:.3f}s")
            print(f"     Rows:   {duck_result['row_count']:,}")
            print(f"   Our Extension:")
            print(f"     Memory: {ext_memory:+.1f} MB")
            print(f"     Time:   {ext_time:.3f}s")
            print(f"     Rows:   {ext_result['row_count']:,}")
            print(f"   Comparison:")
            print(f"     Memory savings: {memory_diff:+.1f} MB ({memory_savings_pct:+.1f}%)")
            print(f"     Time difference: {time_diff:+.3f}s ({time_savings_pct:+.1f}%)")
            
            # Verify row counts match
            if duck_result['row_count'] == ext_result['row_count']:
                print(f"     ✅ Row counts match")
            else:
                print(f"     ❌ Row count mismatch!")
            
            comparisons.append({
                'scenario': scenario,
                'duck_memory': duck_memory,
                'ext_memory': ext_memory,
                'memory_savings': memory_diff,
                'memory_savings_pct': memory_savings_pct,
                'duck_time': duck_time,
                'ext_time': ext_time,
                'time_diff': time_diff,
                'time_savings_pct': time_savings_pct,
                'rows_match': duck_result['row_count'] == ext_result['row_count']
            })
    
    # Overall summary
    if comparisons:
        print(f"\n🏆 OVERALL COMPARISON SUMMARY")
        print("=" * 50)
        
        total_duck_memory = sum(c['duck_memory'] for c in comparisons)
        total_ext_memory = sum(c['ext_memory'] for c in comparisons)
        total_memory_savings = total_duck_memory - total_ext_memory
        total_memory_savings_pct = (total_memory_savings / abs(total_duck_memory) * 100) if total_duck_memory != 0 else 0
        
        total_duck_time = sum(c['duck_time'] for c in comparisons)
        total_ext_time = sum(c['ext_time'] for c in comparisons)
        total_time_diff = total_duck_time - total_ext_time
        total_time_savings_pct = (total_time_diff / total_duck_time * 100) if total_duck_time > 0 else 0
        
        print(f"\n📊 Memory Usage:")
        print(f"   DuckDB Built-in Total: {total_duck_memory:.1f} MB")
        print(f"   Our Extension Total:   {total_ext_memory:.1f} MB")
        print(f"   Total Memory Savings:  {total_memory_savings:+.1f} MB ({total_memory_savings_pct:+.1f}%)")
        
        print(f"\n⏱️  Execution Time:")
        print(f"   DuckDB Built-in Total: {total_duck_time:.3f}s")
        print(f"   Our Extension Total:   {total_ext_time:.3f}s")
        print(f"   Total Time Difference: {total_time_diff:+.3f}s ({total_time_savings_pct:+.1f}%)")
        
        # Identify best scenarios
        projection_comparisons = [c for c in comparisons if 'Projection' in c['scenario']]
        if projection_comparisons:
            best_memory_scenario = max(projection_comparisons, key=lambda x: x['memory_savings'])
            print(f"\n🏅 Best Memory Optimization:")
            print(f"   Scenario: {best_memory_scenario['scenario']}")
            print(f"   Memory Savings: {best_memory_scenario['memory_savings']:+.1f} MB ({best_memory_scenario['memory_savings_pct']:+.1f}%)")
        
        # Correctness check
        all_correct = all(c['rows_match'] for c in comparisons)
        print(f"\n✅ Correctness: {'All tests match' if all_correct else 'Some mismatches found'}")

def main():
    """Main function."""
    compare_results()

if __name__ == "__main__":
    main()
