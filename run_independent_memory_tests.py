#!/usr/bin/env python3
"""
Run independent memory tests for clean comparison.
Each test runs in a separate process to avoid memory contamination.
"""

import subprocess
import sys
import os
import time

def run_script(script_name, description):
    """Run a script in a separate process and capture output."""
    print(f"\n🚀 Running {description}")
    print("=" * 60)
    
    try:
        # Run script in separate process
        result = subprocess.run(
            [sys.executable, script_name],
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            print(result.stdout)
            print(f"✅ {description} completed successfully")
        else:
            print(f"❌ {description} failed with return code {result.returncode}")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {description} timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ {description} failed with error: {e}")
        return False
    
    return True

def main():
    """Run all independent memory tests."""
    print("🧪 Independent Memory Test Suite")
    print("Testing DuckDB built-in vs Our JSON Extension")
    print("=" * 80)
    
    # Check prerequisites
    if not os.path.exists("memory_test_wide.json"):
        print("❌ Test data files not found. Creating them first...")
        if not run_script("create_memory_test_data.py", "Test Data Creation"):
            return
    
    if not os.path.exists("./build/release/json_stream.duckdb_extension"):
        print("❌ Extension not found. Run 'make release' first.")
        return
    
    # Clean up any existing result files
    for result_file in ["duckdb_builtin_results.json", "extension_results.json"]:
        if os.path.exists(result_file):
            os.remove(result_file)
            print(f"🧹 Cleaned up old result file: {result_file}")
    
    # Run tests independently
    tests = [
        ("test_duckdb_builtin_memory.py", "DuckDB Built-in Memory Test"),
        ("test_extension_memory.py", "Our Extension Memory Test"),
        ("compare_memory_results.py", "Memory Comparison Analysis")
    ]
    
    success_count = 0
    
    for script, description in tests:
        if run_script(script, description):
            success_count += 1
        
        # Wait between tests to ensure clean separation
        if script != tests[-1][0]:  # Don't wait after the last test
            print(f"\n⏳ Waiting 3 seconds before next test...")
            time.sleep(3)
    
    # Final summary
    print(f"\n🏁 Test Suite Complete")
    print("=" * 40)
    print(f"✅ Successful tests: {success_count}/{len(tests)}")
    
    if success_count == len(tests):
        print("🎉 All tests completed successfully!")
        print("\n📁 Result files generated:")
        print("   - duckdb_builtin_results.json")
        print("   - extension_results.json")
        print("\n📊 Check the comparison output above for detailed analysis.")
    else:
        print("❌ Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
