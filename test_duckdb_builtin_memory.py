#!/usr/bin/env python3
"""
Independent memory test for DuckDB's built-in JSON reader using memray profiler.
Run this separately from the extension test to avoid memory contamination.
"""

import duckdb
import time
import os
import gc
import sys
import json
import tempfile
import subprocess
from pathlib import Path

def run_duckdb_test_with_memray(file_path: str, query_desc: str, sql_query: str, output_dir: str):
    """Run DuckDB test with memray profiling in a separate process."""

    # Create a temporary script to run with memray
    test_script = f"""
import duckdb
import time
import gc

def run_test():
    # Force garbage collection
    gc.collect()

    start_time = time.time()

    # Create fresh connection
    conn = duckdb.connect()

    # Execute query
    result = conn.execute("{sql_query}".format(file="{file_path}")).fetchall()
    row_count = len(result)

    # Clean up
    conn.close()
    del conn
    gc.collect()

    end_time = time.time()
    execution_time = end_time - start_time

    print(f"Rows: {{row_count}}")
    print(f"Time: {{execution_time:.3f}}")

    return row_count, execution_time

if __name__ == "__main__":
    run_test()
"""

    # Write test script to temporary file
    script_path = os.path.join(output_dir, f"duckdb_test_{hash(query_desc) % 10000}.py")
    with open(script_path, 'w') as f:
        f.write(test_script)

    # Run with memray
    memray_output = os.path.join(output_dir, f"duckdb_{hash(query_desc) % 10000}.bin")

    try:
        # Run memray profiling
        cmd = [
            sys.executable, "-m", "memray", "run",
            "--output", memray_output,
            script_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode != 0:
            print(f"   ❌ Test failed: {result.stderr}")
            return {'success': False, 'error': result.stderr}

        # Parse output for row count and time
        output_lines = result.stdout.strip().split('\n')
        row_count = 0
        execution_time = 0.0

        for line in output_lines:
            if line.startswith("Rows: "):
                row_count = int(line.split(": ")[1])
            elif line.startswith("Time: "):
                execution_time = float(line.split(": ")[1])

        # Get memory stats from memray
        stats_cmd = [
            sys.executable, "-m", "memray", "stats",
            memray_output
        ]

        stats_result = subprocess.run(stats_cmd, capture_output=True, text=True)

        if stats_result.returncode == 0:
            # Parse memray stats
            stats_output = stats_result.stdout
            peak_memory_mb = parse_memray_peak_memory(stats_output)
        else:
            peak_memory_mb = 0.0

        return {
            'success': True,
            'row_count': row_count,
            'execution_time': execution_time,
            'peak_memory_mb': peak_memory_mb,
            'memray_file': memray_output
        }

    except subprocess.TimeoutExpired:
        return {'success': False, 'error': 'Test timed out'}
    except Exception as e:
        return {'success': False, 'error': str(e)}
    finally:
        # Clean up script file
        if os.path.exists(script_path):
            os.remove(script_path)

def parse_memray_peak_memory(stats_output: str) -> float:
    """Parse total memory allocated from memray stats output."""
    try:
        for line in stats_output.split('\n'):
            if '📦 Total memory allocated:' in line:
                # Extract the next line which contains the memory value
                lines = stats_output.split('\n')
                line_index = lines.index(line)
                if line_index + 1 < len(lines):
                    memory_line = lines[line_index + 1].strip()
                    # Parse format like "1.840GB"
                    if 'GB' in memory_line:
                        value = float(memory_line.replace('GB', ''))
                        return value * 1024  # Convert to MB
                    elif 'MB' in memory_line:
                        value = float(memory_line.replace('MB', ''))
                        return value
                    elif 'KB' in memory_line:
                        value = float(memory_line.replace('KB', ''))
                        return value / 1024  # Convert to MB
                    elif 'B' in memory_line and 'MB' not in memory_line and 'KB' not in memory_line:
                        value = float(memory_line.replace('B', ''))
                        return value / (1024 * 1024)  # Convert to MB
        return 0.0
    except Exception as e:
        print(f"Error parsing memray output: {e}")
        return 0.0

def test_duckdb_builtin(file_path: str, query_desc: str, sql_query: str, output_dir: str):
    """Test DuckDB's built-in JSON reader with memray profiling."""
    print(f"\n🔍 DuckDB Built-in JSON - {query_desc}")
    print(f"   File: {file_path}")

    result = run_duckdb_test_with_memray(file_path, query_desc, sql_query, output_dir)

    if result['success']:
        print(f"   Peak memory: {result['peak_memory_mb']:.1f} MB")
        print(f"   Execution time: {result['execution_time']:.3f}s")
        print(f"   Rows returned: {result['row_count']:,}")
        print(f"   Memray profile: {result['memray_file']}")
    else:
        print(f"   ❌ Error: {result.get('error', 'Unknown error')}")

    return result

def main():
    """Run DuckDB built-in JSON tests with memray profiling."""
    print("🧪 DuckDB Built-in JSON Reader - Memory Test (with memray)")
    print("=" * 70)

    # Create output directory for memray files
    output_dir = "memray_profiles"
    os.makedirs(output_dir, exist_ok=True)

    # Test scenarios
    scenarios = [
        {
            "file": "memory_test_wide.json",
            "name": "Wide File Projection (2/100 fields)",
            "query": "SELECT id, name FROM read_json('{file}')"
        },
        {
            "file": "memory_test_wide.json",
            "name": "Wide File Full Scan",
            "query": "SELECT COUNT(*) FROM read_json('{file}')"
        },
        {
            "file": "memory_test_deep.json",
            "name": "Deep File Projection (skip nested data)",
            "query": "SELECT id, target_field FROM read_json('{file}')"
        },
        {
            "file": "memory_test_deep.json",
            "name": "Deep File Full Scan",
            "query": "SELECT COUNT(*) FROM read_json('{file}')"
        }
    ]

    results = []

    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 Test {i}/{len(scenarios)}: {scenario['name']}")
        print("-" * 50)

        # Check if file exists
        if not os.path.exists(scenario['file']):
            print(f"   ❌ File not found: {scenario['file']}")
            continue

        # Run test with memray
        result = test_duckdb_builtin(
            scenario['file'],
            scenario['name'],
            scenario['query'],
            output_dir
        )

        result['scenario'] = scenario['name']
        result['file'] = scenario['file']
        results.append(result)

        # Wait a bit between tests
        time.sleep(1)

    # Save results to JSON file
    output_file = "duckdb_builtin_results.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\n💾 Results saved to: {output_file}")
    print(f"📁 Memray profiles saved to: {output_dir}/")

    # Summary
    print(f"\n📊 DuckDB Built-in Summary:")
    print("=" * 40)

    successful_tests = [r for r in results if r.get('success', False)]

    for result in successful_tests:
        print(f"\n📈 {result['scenario']}:")
        print(f"   Peak memory: {result['peak_memory_mb']:.1f} MB")
        print(f"   Execution time: {result['execution_time']:.3f}s")
        print(f"   Rows: {result['row_count']:,}")

    if successful_tests:
        total_memory = sum(r['peak_memory_mb'] for r in successful_tests)
        total_time = sum(r['execution_time'] for r in successful_tests)
        print(f"\n🎯 Total Peak Memory: {total_memory:.1f} MB")
        print(f"🎯 Total Execution Time: {total_time:.3f}s")

        # Show memray analysis commands
        print(f"\n📊 Memray Analysis Commands:")
        print("To analyze memory usage in detail, run:")
        for result in successful_tests:
            if 'memray_file' in result:
                memray_file = result['memray_file']
                print(f"  memray flamegraph {memray_file}")
                print(f"  memray table {memray_file}")
                print(f"  memray summary {memray_file}")
                print()

if __name__ == "__main__":
    main()
