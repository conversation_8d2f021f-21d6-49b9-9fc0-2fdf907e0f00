#!/usr/bin/env python3
"""
Independent memory test for DuckDB's built-in JSON reader.
Run this separately from the extension test to avoid memory contamination.
"""

import duckdb
import time
import psutil
import os
import gc
import sys
import json

class MemoryProfiler:
    """Simple memory profiler to track memory usage."""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.baseline = self.get_memory_mb()
    
    def get_memory_mb(self):
        """Get current memory usage in MB."""
        return self.process.memory_info().rss / (1024 * 1024)
    
    def get_peak_memory_mb(self):
        """Get peak memory usage in MB."""
        try:
            return self.process.memory_info().peak_wset / (1024 * 1024)
        except AttributeError:
            # peak_wset not available on all platforms
            return self.get_memory_mb()

def test_duckdb_builtin(file_path, query_desc, sql_query):
    """Test DuckDB's built-in JSON reader with clean memory measurement."""
    print(f"\n🔍 DuckDB Built-in JSON - {query_desc}")
    print(f"   File: {file_path}")
    
    # Force garbage collection before starting
    gc.collect()
    
    profiler = MemoryProfiler()
    start_memory = profiler.get_memory_mb()
    start_time = time.time()
    
    print(f"   Starting memory: {start_memory:.1f} MB")
    
    try:
        # Create fresh connection
        conn = duckdb.connect()
        
        # Execute query
        result = conn.execute(sql_query.format(file=file_path)).fetchall()
        row_count = len(result)
        
        # Clean up connection
        conn.close()
        del conn
        
        # Force garbage collection
        gc.collect()
        
        end_time = time.time()
        end_memory = profiler.get_memory_mb()
        peak_memory = profiler.get_peak_memory_mb()
        
        memory_used = end_memory - start_memory
        execution_time = end_time - start_time
        
        print(f"   Ending memory: {end_memory:.1f} MB")
        print(f"   Peak memory: {peak_memory:.1f} MB")
        print(f"   Memory used: {memory_used:+.1f} MB")
        print(f"   Execution time: {execution_time:.3f}s")
        print(f"   Rows returned: {row_count:,}")
        
        return {
            'success': True,
            'row_count': row_count,
            'start_memory': start_memory,
            'end_memory': end_memory,
            'peak_memory': peak_memory,
            'memory_used': memory_used,
            'execution_time': execution_time
        }
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """Run DuckDB built-in JSON tests."""
    print("🧪 DuckDB Built-in JSON Reader - Memory Test")
    print("=" * 60)
    
    # Test scenarios
    scenarios = [
        {
            "file": "memory_test_wide.json",
            "name": "Wide File Projection (2/100 fields)",
            "query": "SELECT id, name FROM read_json('{file}')"
        },
        {
            "file": "memory_test_wide.json", 
            "name": "Wide File Full Scan",
            "query": "SELECT COUNT(*) FROM read_json('{file}')"
        },
        {
            "file": "memory_test_deep.json",
            "name": "Deep File Projection (skip nested data)",
            "query": "SELECT id, target_field FROM read_json('{file}')"
        },
        {
            "file": "memory_test_deep.json",
            "name": "Deep File Full Scan", 
            "query": "SELECT COUNT(*) FROM read_json('{file}')"
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 Test {i}/{len(scenarios)}: {scenario['name']}")
        print("-" * 50)
        
        # Check if file exists
        if not os.path.exists(scenario['file']):
            print(f"   ❌ File not found: {scenario['file']}")
            continue
        
        # Run test
        result = test_duckdb_builtin(
            scenario['file'], 
            scenario['name'], 
            scenario['query']
        )
        
        result['scenario'] = scenario['name']
        result['file'] = scenario['file']
        results.append(result)
        
        # Wait a bit between tests
        time.sleep(1)
    
    # Save results to JSON file
    output_file = "duckdb_builtin_results.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    # Summary
    print(f"\n📊 DuckDB Built-in Summary:")
    print("=" * 40)
    
    successful_tests = [r for r in results if r.get('success', False)]
    
    for result in successful_tests:
        print(f"\n📈 {result['scenario']}:")
        print(f"   Memory used: {result['memory_used']:+.1f} MB")
        print(f"   Execution time: {result['execution_time']:.3f}s")
        print(f"   Rows: {result['row_count']:,}")
    
    if successful_tests:
        total_memory = sum(r['memory_used'] for r in successful_tests)
        total_time = sum(r['execution_time'] for r in successful_tests)
        print(f"\n🎯 Total Memory Used: {total_memory:.1f} MB")
        print(f"🎯 Total Execution Time: {total_time:.3f}s")

if __name__ == "__main__":
    main()
