
import duckdb
import time
import gc

def run_test():
    # Force garbage collection
    gc.collect()

    start_time = time.time()

    # Create fresh connection with unsigned extensions enabled
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})

    # Load our extension (release version)
    conn.execute("LOAD './build/release/json_stream.duckdb_extension'")

    # Execute query
    result = conn.execute("SELECT id, name FROM json_stream('{file}')".format(file="memory_test_wide.json")).fetchall()
    row_count = len(result)

    # Clean up
    conn.close()
    del conn
    gc.collect()

    end_time = time.time()
    execution_time = end_time - start_time

    print(f"Rows: {row_count}")
    print(f"Time: {execution_time:.3f}")

    return row_count, execution_time

if __name__ == "__main__":
    run_test()
