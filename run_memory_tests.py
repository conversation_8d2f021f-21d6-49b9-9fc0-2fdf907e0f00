#!/usr/bin/env python3
"""
Run memory comparison tests using pytest-memray and generate a report.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_memory_tests():
    """Run memory tests with memray profiling."""
    print("🧪 Running Memory Comparison Tests with pytest-memray")
    print("=" * 60)
    
    # Check if extension exists
    if not Path("./build/release/json_stream.duckdb_extension").exists():
        print("❌ Extension not found. Run 'make release' first.")
        return False
    
    # Create memray output directory
    memray_dir = Path("memray_profiles")
    memray_dir.mkdir(exist_ok=True)
    
    # Run memory tests
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/test_memory_comparison.py::TestMemoryComparison",
        "-v",
        "--memray",
        f"--memray-bin-path={memray_dir}",
        "--memray-bin-prefix=test_",
        "-m", "memory"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ Memory tests completed successfully!")
        
        # List generated memray files
        memray_files = list(memray_dir.glob("*.bin"))
        if memray_files:
            print(f"\n📁 Generated {len(memray_files)} memray profile(s):")
            for f in sorted(memray_files):
                print(f"   {f}")
            
            print(f"\n📊 To analyze memory usage, run:")
            print(f"   memray table {memray_files[0]}")
            print(f"   memray flamegraph {memray_files[0]}")
            print(f"   memray summary {memray_files[0]}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Memory tests failed with return code {e.returncode}")
        return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def run_projection_tests():
    """Run projection pushdown correctness tests."""
    print("\n🎯 Running Projection Pushdown Tests")
    print("=" * 40)
    
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/test_memory_comparison.py::TestProjectionPushdown",
        "-v",
        "-m", "projection"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ Projection tests completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Projection tests failed with return code {e.returncode}")
        return False


def run_performance_tests():
    """Run basic performance tests."""
    print("\n⚡ Running Performance Baseline Tests")
    print("=" * 40)
    
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/test_memory_comparison.py::TestPerformanceBaseline",
        "-v", "-s",  # -s to show print output
        "-m", "performance"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ Performance tests completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Performance tests failed with return code {e.returncode}")
        return False


def main():
    """Main function to run all tests."""
    print("🚀 JSON Streaming Extension - Memory & Performance Test Suite")
    print("=" * 70)
    
    success_count = 0
    total_tests = 3
    
    # Run memory tests
    if run_memory_tests():
        success_count += 1
    
    # Run projection tests
    if run_projection_tests():
        success_count += 1
    
    # Run performance tests
    if run_performance_tests():
        success_count += 1
    
    # Summary
    print(f"\n🏁 Test Suite Summary")
    print("=" * 30)
    print(f"✅ Successful test suites: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 All test suites passed!")
        print("\n📋 Next Steps:")
        print("1. Review memray profiles for memory usage analysis")
        print("2. Compare memory usage between DuckDB built-in and extension")
        print("3. Verify projection pushdown is working correctly")
        return True
    else:
        print("❌ Some test suites failed. Check output above for details.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
