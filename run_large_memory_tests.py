#!/usr/bin/env python3
"""
Run large-scale memory comparison tests to demonstrate GB-scale memory differences.
"""

import subprocess
import sys
import os
from pathlib import Path
import time


def run_large_memory_tests():
    """Run memory tests with large datasets."""
    print("🧪 Running Large-Scale Memory Comparison Tests")
    print("=" * 60)
    print("📊 Dataset sizes:")
    print("   - Wide JSON: 10,000 rows × 50 unused fields each")
    print("   - Deep JSON: 2,000 rows × deeply nested structures")
    print("   - Expected DuckDB memory usage: 1-5 GB")
    print("   - Expected Extension memory usage: <100 MB")
    print()
    
    # Check if extension exists
    if not Path("./build/release/json_stream.duckdb_extension").exists():
        print("❌ Extension not found. Run 'make release' first.")
        return False
    
    # Create memray output directory
    memray_dir = Path("memray_profiles_large")
    memray_dir.mkdir(exist_ok=True)
    
    # Run memory tests
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/test_memory_comparison.py::TestMemoryComparison",
        "-v", "-s",
        "--memray",
        f"--memray-bin-path={memray_dir}",
        "--memray-bin-prefix=large_test_",
        "-m", "memory"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    print()
    print("⚠️  Warning: This may take several minutes and use significant memory!")
    print("   DuckDB built-in tests may use 1-5 GB of RAM")
    print()
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n✅ Large memory tests completed successfully!")
        print(f"⏱️  Total execution time: {total_time:.1f} seconds")
        
        # List generated memray files
        memray_files = list(memray_dir.glob("*.bin"))
        if memray_files:
            print(f"\n📁 Generated {len(memray_files)} memray profile(s):")
            for f in sorted(memray_files):
                print(f"   {f}")
            
            print(f"\n📊 To analyze memory usage, run:")
            print(f"   memray table {memray_files[0]}")
            print(f"   memray flamegraph {memray_files[0]}")
            print(f"   memray summary {memray_files[0]}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Large memory tests failed with return code {e.returncode}")
        return False
    except KeyboardInterrupt:
        print(f"\n⚠️  Tests interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def estimate_file_sizes():
    """Estimate the size of test files that will be generated."""
    print("📏 Estimating test file sizes...")
    
    # Rough estimates based on JSON structure
    wide_rows = 10000
    wide_fields_per_row = 53  # 3 main + 50 unused
    wide_avg_field_size = 500  # bytes per field (including nested structures)
    wide_estimated_size = wide_rows * wide_fields_per_row * wide_avg_field_size
    
    deep_rows = 2000
    deep_avg_row_size = 15000  # bytes per row (deeply nested)
    deep_estimated_size = deep_rows * deep_avg_row_size
    
    print(f"   Wide JSON file: ~{wide_estimated_size / (1024*1024):.1f} MB")
    print(f"   Deep JSON file: ~{deep_estimated_size / (1024*1024):.1f} MB")
    print(f"   Total disk space: ~{(wide_estimated_size + deep_estimated_size) / (1024*1024):.1f} MB")
    print()


def main():
    """Main function to run large-scale memory tests."""
    print("🚀 JSON Streaming Extension - Large-Scale Memory Test Suite")
    print("=" * 70)
    
    estimate_file_sizes()
    
    # Ask for confirmation
    response = input("⚠️  This will generate large test files and use significant memory. Continue? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("❌ Tests cancelled by user")
        return False
    
    print("\n🏁 Starting large-scale memory tests...")
    success = run_large_memory_tests()
    
    # Summary
    print(f"\n🏁 Large-Scale Test Suite Summary")
    print("=" * 40)
    
    if success:
        print("🎉 Large-scale memory tests completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Review memray profiles for GB-scale memory usage comparison")
        print("2. Compare memory efficiency between DuckDB built-in and extension")
        print("3. Verify extension handles large datasets without memory issues")
        print("\n💡 Expected Results:")
        print("   - DuckDB built-in: 1-5 GB memory usage")
        print("   - Our extension: <100 MB memory usage")
        print("   - Memory savings: 90-99% reduction")
        return True
    else:
        print("❌ Large-scale memory tests failed. Check output above for details.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
